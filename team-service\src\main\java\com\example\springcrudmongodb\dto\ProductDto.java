package com.example.springcrudmongodb.dto;

/**
 * Data Transfer Object (DTO) for representing product information.
 *
 * <p>
 * A DTO is a design pattern used to transfer data between different layers of an application,
 * such as between the service layer and the presentation layer. It encapsulates data and
 * reduces the number of method calls, improving performance and decoupling the layers.
 *
 * <p>
 * This DTO uses the {@code record} feature introduced in Java 14. A {@code record} is a
 * concise way to define immutable data-carrying classes. It automatically provides
 * implementations for {@code equals()}, {@code hashCode()}, {@code toString()}, and getter
 * methods, reducing boilerplate code and ensuring immutability.
 * <p>
 *
 * The use of {@code record} in this DTO ensures that the data is immutable, thread-safe,
 * and easy to work with, while maintaining a clean and simple structure.
 *
 * @param id The unique identifier for the product.
 * @param name The name of the product.
 * @param quantity The quantity of the product available in stock.
 */
public record ProductDto(String id,String name,int quantity) {
    // No additional methods are needed as the record automatically provides
    // getters, equals, hashCode, and toString methods.
}