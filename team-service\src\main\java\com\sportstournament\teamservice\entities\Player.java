package com.sportstournament.teamservice.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Player {
    
    private String playerId;
    
    private String firstName;
    
    private String lastName;
    
    private LocalDate dateOfBirth;
    
    private String position;
    
    private String email;
    
    private String phone;
    
    private String jerseyNumber;
    
    private boolean isCaptain;
    
    private PlayerStatus status;
    
    public enum PlayerStatus {
        ACTIVE,
        INACTIVE,
        INJURED,
        SUSPENDED
    }
}
