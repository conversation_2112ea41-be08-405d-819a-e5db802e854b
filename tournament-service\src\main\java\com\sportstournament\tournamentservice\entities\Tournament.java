package com.sportstournament.tournamentservice.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "tournaments")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Tournament {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, unique = true)
    private String name;
    
    @Column(length = 1000)
    private String description;
    
    @Column(name = "sport_type", nullable = false)
    private String sportType;
    
    @Column(name = "max_teams")
    private Integer maxTeams;
    
    @Column(name = "registration_start")
    private LocalDateTime registrationStart;
    
    @Column(name = "registration_end")
    private LocalDateTime registrationEnd;
    
    @Column(name = "tournament_start")
    private LocalDateTime tournamentStart;
    
    @Column(name = "tournament_end")
    private LocalDateTime tournamentEnd;
    
    @Column(name = "entry_fee")
    private Double entryFee;
    
    @Column(name = "prize_pool")
    private Double prizePool;
    
    @Column(name = "tournament_format")
    @Enumerated(EnumType.STRING)
    private TournamentFormat format;
    
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private TournamentStatus status;
    
    @Column(name = "organizer_name")
    private String organizerName;
    
    @Column(name = "organizer_email")
    private String organizerEmail;
    
    @Column(name = "organizer_phone")
    private String organizerPhone;
    
    @Column(name = "venue_name")
    private String venueName;
    
    @Column(name = "venue_address")
    private String venueAddress;
    
    @Column(name = "rules_and_regulations", length = 2000)
    private String rulesAndRegulations;
    
    @OneToMany(mappedBy = "tournament", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Match> matches;
    
    @OneToMany(mappedBy = "tournament", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TournamentTeam> registeredTeams;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum TournamentStatus {
        DRAFT,
        REGISTRATION_OPEN,
        REGISTRATION_CLOSED,
        IN_PROGRESS,
        COMPLETED,
        CANCELLED
    }
    
    public enum TournamentFormat {
        SINGLE_ELIMINATION,
        DOUBLE_ELIMINATION,
        ROUND_ROBIN,
        SWISS_SYSTEM,
        GROUP_STAGE_KNOCKOUT
    }
}
