package com.sportstournament.teamservice.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

@Document(collection = "teams")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Team {
    
    @Id
    private String id;
    
    private String name;
    
    private String description;
    
    private String coach;
    
    private String contactEmail;
    
    private String contactPhone;
    
    private List<Player> players;
    
    private LocalDateTime registrationDate;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private TeamStatus status;
    
    public enum TeamStatus {
        PENDING,
        APPROVED,
        REJECTED,
        ACTIVE,
        INACTIVE
    }
}
