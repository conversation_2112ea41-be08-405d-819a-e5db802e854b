package com.example.springcrudmongodb.controllers;

import com.example.springcrudmongodb.dto.ProductDto;
import com.example.springcrudmongodb.services.IProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


@RestController
@RequestMapping("/products")
@RequiredArgsConstructor
public class ProductRestController{

    private final IProductService productService;

    @PostMapping
    public ProductDto add(@RequestBody ProductDto productDto) {
        return productService.add(productDto);
    }

    @PatchMapping("{id}")
    public ProductDto patchUpdate(@RequestBody Map<Object,Object> fields, @PathVariable String id){
        return productService.update(id,fields);
    }

    @DeleteMapping("{id}")
    public boolean delete( @PathVariable String id){
        return productService.delete(id);
    }


    @GetMapping
    public Page<ProductDto> getProducts(int pageNbr,int pageSize){
        return productService.getProducts(pageNbr,pageSize);
    }

    @GetMapping("{id}")
    public ProductDto getProduct(@PathVariable String id){
        return productService.getProduct(id);
    }

    @GetMapping("name/{name}")
    public ProductDto getProductByName(@PathVariable String name){
        return productService.getProductByName(name);
    }







}