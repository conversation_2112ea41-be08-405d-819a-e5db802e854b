package com.sportstournament.tournamentservice.repositories;

import com.sportstournament.tournamentservice.entities.Match;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface MatchRepository extends JpaRepository<Match, Long> {
    
    List<Match> findByTournamentId(Long tournamentId);
    
    List<Match> findByStatus(Match.MatchStatus status);
    
    @Query("SELECT m FROM Match m WHERE m.team1Id = :teamId OR m.team2Id = :teamId")
    List<Match> findByTeamId(@Param("teamId") String teamId);
    
    @Query("SELECT m FROM Match m WHERE m.tournament.id = :tournamentId AND m.roundNumber = :roundNumber")
    List<Match> findByTournamentIdAndRoundNumber(@Param("tournamentId") Long tournamentId, @Param("roundNumber") Integer roundNumber);
    
    @Query("SELECT m FROM Match m WHERE m.scheduledTime BETWEEN :start AND :end")
    List<Match> findMatchesBetweenDates(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);
    
    @Query("SELECT m FROM Match m WHERE (m.team1Id = :teamId OR m.team2Id = :teamId) AND m.tournament.id = :tournamentId")
    List<Match> findByTeamIdAndTournamentId(@Param("teamId") String teamId, @Param("tournamentId") Long tournamentId);
    
    @Query("SELECT m FROM Match m WHERE m.winnerTeamId = :teamId")
    List<Match> findMatchesWonByTeam(@Param("teamId") String teamId);
    
    @Query("SELECT COUNT(m) FROM Match m WHERE m.tournament.id = :tournamentId AND m.status = :status")
    long countByTournamentIdAndStatus(@Param("tournamentId") Long tournamentId, @Param("status") Match.MatchStatus status);
    
    @Query("SELECT m FROM Match m WHERE m.tournament.id = :tournamentId ORDER BY m.roundNumber, m.matchNumber")
    List<Match> findByTournamentIdOrderByRoundAndMatch(@Param("tournamentId") Long tournamentId);
}
