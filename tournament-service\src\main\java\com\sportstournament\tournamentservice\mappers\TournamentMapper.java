package com.sportstournament.tournamentservice.mappers;

import com.sportstournament.tournamentservice.dto.TournamentDto;
import com.sportstournament.tournamentservice.entities.Tournament;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring", uses = {MatchMapper.class, TournamentTeamMapper.class})
public interface TournamentMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "matches", ignore = true)
    @Mapping(target = "registeredTeams", ignore = true)
    Tournament mapToEntity(TournamentDto tournamentDto);
    
    TournamentDto mapToDto(Tournament tournament);
    
    List<TournamentDto> mapToDtoList(List<Tournament> tournaments);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "matches", ignore = true)
    @Mapping(target = "registeredTeams", ignore = true)
    void updateEntityFromDto(TournamentDto tournamentDto, @MappingTarget Tournament tournament);
}
