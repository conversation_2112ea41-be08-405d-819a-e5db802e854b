package com.sportstournament.tournamentservice.mappers;

import com.sportstournament.tournamentservice.dto.TournamentTeamDto;
import com.sportstournament.tournamentservice.entities.TournamentTeam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TournamentTeamMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tournament", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    TournamentTeam mapToEntity(TournamentTeamDto tournamentTeamDto);
    
    @Mapping(source = "tournament.id", target = "tournamentId")
    TournamentTeamDto mapToDto(TournamentTeam tournamentTeam);
    
    List<TournamentTeamDto> mapToDtoList(List<TournamentTeam> tournamentTeams);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tournament", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromDto(TournamentTeamDto tournamentTeamDto, @MappingTarget TournamentTeam tournamentTeam);
}
