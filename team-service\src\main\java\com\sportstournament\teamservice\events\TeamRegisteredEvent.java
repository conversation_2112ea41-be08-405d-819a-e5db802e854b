package com.sportstournament.teamservice.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TeamRegisteredEvent {
    
    private String teamId;
    private String teamName;
    private String coach;
    private String contactEmail;
    private int playerCount;
    private LocalDateTime timestamp;
}
