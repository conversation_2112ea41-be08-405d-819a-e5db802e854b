package com.sportstournament.tournamentservice.dto;

import com.sportstournament.tournamentservice.entities.Tournament;
import jakarta.validation.constraints.*;

import java.time.LocalDateTime;
import java.util.List;

public record TournamentDto(
        Long id,
        
        @NotBlank(message = "Tournament name is required")
        @Size(min = 3, max = 100, message = "Tournament name must be between 3 and 100 characters")
        String name,
        
        @Size(max = 1000, message = "Description cannot exceed 1000 characters")
        String description,
        
        @NotBlank(message = "Sport type is required")
        String sportType,
        
        @Min(value = 2, message = "Maximum teams must be at least 2")
        @Max(value = 128, message = "Maximum teams cannot exceed 128")
        Integer maxTeams,
        
        @Future(message = "Registration start must be in the future")
        LocalDateTime registrationStart,
        
        @Future(message = "Registration end must be in the future")
        LocalDateTime registrationEnd,
        
        @Future(message = "Tournament start must be in the future")
        LocalDateTime tournamentStart,
        
        @Future(message = "Tournament end must be in the future")
        LocalDateTime tournamentEnd,
        
        @DecimalMin(value = "0.0", message = "Entry fee cannot be negative")
        Double entryFee,
        
        @DecimalMin(value = "0.0", message = "Prize pool cannot be negative")
        Double prizePool,
        
        @NotNull(message = "Tournament format is required")
        Tournament.TournamentFormat format,
        
        Tournament.TournamentStatus status,
        
        @NotBlank(message = "Organizer name is required")
        String organizerName,
        
        @NotBlank(message = "Organizer email is required")
        @Email(message = "Invalid email format")
        String organizerEmail,
        
        String organizerPhone,
        
        String venueName,
        
        String venueAddress,
        
        @Size(max = 2000, message = "Rules and regulations cannot exceed 2000 characters")
        String rulesAndRegulations,
        
        List<MatchDto> matches,
        
        List<TournamentTeamDto> registeredTeams,
        
        LocalDateTime createdAt,
        
        LocalDateTime updatedAt
) {}
