package com.sportstournament.teamservice.repositories;

import com.sportstournament.teamservice.entities.Team;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TeamRepository extends MongoRepository<Team, String> {
    
    Optional<Team> findByName(String name);
    
    List<Team> findByStatus(Team.TeamStatus status);
    
    List<Team> findByCoach(String coach);
    
    @Query("{'contactEmail': ?0}")
    Optional<Team> findByContactEmail(String contactEmail);
    
    @Query("{'name': {$regex: ?0, $options: 'i'}}")
    List<Team> findByNameContainingIgnoreCase(String name);
    
    boolean existsByName(String name);
    
    boolean existsByContactEmail(String contactEmail);
    
    long countByStatus(Team.TeamStatus status);
}
