package com.example.springcrudmongodb.mappers;

import com.example.springcrudmongodb.dto.ProductDto;
import com.example.springcrudmongodb.entities.Product;
import org.mapstruct.Mapper;

/**
 * Mapper interface for converting between {@link Product} entities and {@link ProductDto} DTOs.
 * <p>
 * This interface uses the <strong>MapStruct</strong> library to automatically generate
 * mapping implementations at compile time. The {@code @Mapper} annotation indicates that
 * this interface is a MapStruct mapper.
 * <p>
 * The {@code componentModel = "spring"} attribute specifies that the generated mapper
 * implementation should be a Spring bean, allowing it to be injected and managed by the
 * Spring framework.
 * <p>
 * MapStruct eliminates the need for manual mapping code by generating efficient and
 * type-safe mapping implementations.
 *
 * @see <a href="https://mapstruct.org/">MapStruct Documentation</a>
 */
@Mapper(componentModel = "spring")
public interface ProductMapper {

    /**
     * Converts a {@link ProductDto} object to a {@link Product} entity.
     *
     * @param productDto the {@link ProductDto} object to be mapped
     * @return the corresponding {@link Product} entity
     */
    Product mapToEntity(ProductDto productDto);

    /**
     * Converts a {@link Product} entity to a {@link ProductDto} object.
     *
     * @param product the {@link Product} entity to be mapped
     * @return the corresponding {@link ProductDto} object
     */
    ProductDto mapToDto(Product product);
}