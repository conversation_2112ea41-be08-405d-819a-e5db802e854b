package com.sportstournament.teamservice.controllers;

import com.sportstournament.teamservice.dto.TeamDto;
import com.sportstournament.teamservice.entities.Team;
import com.sportstournament.teamservice.services.TeamService;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/teams")
@RequiredArgsConstructor
@Slf4j
public class TeamController {
    
    private final TeamService teamService;
    
    @PostMapping
    public ResponseEntity<TeamDto> registerTeam(@Valid @RequestBody TeamDto teamDto) {
        log.info("Received request to register team: {}", teamDto.getName());
        TeamDto registeredTeam = teamService.registerTeam(teamDto);
        return new ResponseEntity<>(registeredTeam, HttpStatus.CREATED);
    }
    
    @GetMapping
    public ResponseEntity<List<TeamDto>> getAllTeams() {
        log.info("Received request to get all teams");
        List<TeamDto> teams = teamService.getAllTeams();
        return ResponseEntity.ok(teams);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<TeamDto> getTeamById(@PathVariable String id) {
        log.info("Received request to get team by ID: {}", id);
        TeamDto team = teamService.getTeamById(id);
        return ResponseEntity.ok(team);
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<TeamDto> updateTeam(@PathVariable String id, @Valid @RequestBody TeamDto teamDto) {
        log.info("Received request to update team with ID: {}", id);
        TeamDto updatedTeam = teamService.updateTeam(id, teamDto);
        return ResponseEntity.ok(updatedTeam);
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTeam(@PathVariable String id) {
        log.info("Received request to delete team with ID: {}", id);
        teamService.deleteTeam(id);
        return ResponseEntity.noContent().build();
    }
    
    @GetMapping("/status/{status}")
    public ResponseEntity<List<TeamDto>> getTeamsByStatus(@PathVariable Team.TeamStatus status) {
        log.info("Received request to get teams by status: {}", status);
        List<TeamDto> teams = teamService.getTeamsByStatus(status);
        return ResponseEntity.ok(teams);
    }
    
    @PatchMapping("/{id}/approve")
    public ResponseEntity<TeamDto> approveTeam(@PathVariable String id) {
        log.info("Received request to approve team with ID: {}", id);
        TeamDto approvedTeam = teamService.approveTeam(id);
        return ResponseEntity.ok(approvedTeam);
    }
    
    @PatchMapping("/{id}/reject")
    public ResponseEntity<TeamDto> rejectTeam(@PathVariable String id) {
        log.info("Received request to reject team with ID: {}", id);
        TeamDto rejectedTeam = teamService.rejectTeam(id);
        return ResponseEntity.ok(rejectedTeam);
    }
    
    @GetMapping("/search")
    public ResponseEntity<List<TeamDto>> searchTeamsByName(@RequestParam String name) {
        log.info("Received request to search teams by name: {}", name);
        List<TeamDto> teams = teamService.searchTeamsByName(name);
        return ResponseEntity.ok(teams);
    }
    
    @GetMapping("/exists/name/{name}")
    public ResponseEntity<Boolean> checkTeamNameExists(@PathVariable String name) {
        log.info("Received request to check if team name exists: {}", name);
        boolean exists = teamService.existsByName(name);
        return ResponseEntity.ok(exists);
    }
    
    @GetMapping("/exists/email/{email}")
    public ResponseEntity<Boolean> checkTeamEmailExists(@PathVariable String email) {
        log.info("Received request to check if team email exists: {}", email);
        boolean exists = teamService.existsByContactEmail(email);
        return ResponseEntity.ok(exists);
    }
    
    @GetMapping("/count/status/{status}")
    public ResponseEntity<Long> countTeamsByStatus(@PathVariable Team.TeamStatus status) {
        log.info("Received request to count teams by status: {}", status);
        long count = teamService.countTeamsByStatus(status);
        return ResponseEntity.ok(count);
    }
}
