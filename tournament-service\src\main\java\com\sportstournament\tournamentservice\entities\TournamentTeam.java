package com.sportstournament.tournamentservice.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "tournament_teams")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TournamentTeam {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tournament_id", nullable = false)
    private Tournament tournament;
    
    @Column(name = "team_id", nullable = false)
    private String teamId;
    
    @Column(name = "team_name", nullable = false)
    private String teamName;
    
    @Column(name = "registration_date")
    private LocalDateTime registrationDate;
    
    @Column(name = "registration_status")
    @Enumerated(EnumType.STRING)
    private RegistrationStatus status;
    
    @Column(name = "seed_number")
    private Integer seedNumber;
    
    @Column(name = "group_name")
    private String groupName;
    
    @Column(name = "wins")
    private Integer wins = 0;
    
    @Column(name = "losses")
    private Integer losses = 0;
    
    @Column(name = "draws")
    private Integer draws = 0;
    
    @Column(name = "points")
    private Integer points = 0;
    
    @Column(name = "goals_for")
    private Integer goalsFor = 0;
    
    @Column(name = "goals_against")
    private Integer goalsAgainst = 0;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum RegistrationStatus {
        PENDING,
        CONFIRMED,
        REJECTED,
        WITHDRAWN
    }
}
