package com.sportstournament.tournamentservice.repositories;

import com.sportstournament.tournamentservice.entities.Tournament;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TournamentRepository extends JpaRepository<Tournament, Long> {
    
    Optional<Tournament> findByName(String name);
    
    List<Tournament> findByStatus(Tournament.TournamentStatus status);
    
    List<Tournament> findBySportType(String sportType);
    
    List<Tournament> findByOrganizerEmail(String organizerEmail);
    
    @Query("SELECT t FROM Tournament t WHERE t.name LIKE %:name%")
    List<Tournament> findByNameContainingIgnoreCase(@Param("name") String name);
    
    @Query("SELECT t FROM Tournament t WHERE t.registrationStart <= :now AND t.registrationEnd >= :now")
    List<Tournament> findTournamentsWithOpenRegistration(@Param("now") LocalDateTime now);
    
    @Query("SELECT t FROM Tournament t WHERE t.tournamentStart BETWEEN :start AND :end")
    List<Tournament> findTournamentsBetweenDates(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);
    
    @Query("SELECT t FROM Tournament t WHERE t.sportType = :sportType AND t.status = :status")
    List<Tournament> findBySportTypeAndStatus(@Param("sportType") String sportType, @Param("status") Tournament.TournamentStatus status);
    
    boolean existsByName(String name);
    
    long countByStatus(Tournament.TournamentStatus status);
    
    @Query("SELECT COUNT(t) FROM Tournament t WHERE t.organizerEmail = :email")
    long countByOrganizerEmail(@Param("email") String email);
}
