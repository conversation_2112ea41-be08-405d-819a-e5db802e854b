package com.sportstournament.teamservice.dto;

import com.sportstournament.teamservice.entities.Player;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Past;
import jakarta.validation.constraints.Size;

import java.time.LocalDate;

public record PlayerDto(
        String playerId,
        
        @NotBlank(message = "First name is required")
        @Size(min = 2, max = 50, message = "First name must be between 2 and 50 characters")
        String firstName,
        
        @NotBlank(message = "Last name is required")
        @Size(min = 2, max = 50, message = "Last name must be between 2 and 50 characters")
        String lastName,
        
        @NotNull(message = "Date of birth is required")
        @Past(message = "Date of birth must be in the past")
        LocalDate dateOfBirth,
        
        @NotBlank(message = "Position is required")
        String position,
        
        @Email(message = "Invalid email format")
        String email,
        
        String phone,
        
        @NotBlank(message = "Jersey number is required")
        String jerseyNumber,
        
        boolean isCaptain,
        
        Player.PlayerStatus status
) {}
