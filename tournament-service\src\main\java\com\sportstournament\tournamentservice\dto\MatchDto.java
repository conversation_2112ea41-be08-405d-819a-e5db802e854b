package com.sportstournament.tournamentservice.dto;

import com.sportstournament.tournamentservice.entities.Match;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;

import java.time.LocalDateTime;

public record MatchDto(
        Long id,
        
        @NotNull(message = "Tournament ID is required")
        Long tournamentId,
        
        @NotBlank(message = "Team 1 ID is required")
        String team1Id,
        
        String team1Name,
        
        @NotBlank(message = "Team 2 ID is required")
        String team2Id,
        
        String team2Name,
        
        LocalDateTime scheduledTime,
        
        LocalDateTime actualStartTime,
        
        LocalDateTime actualEndTime,
        
        @Min(value = 0, message = "Team 1 score cannot be negative")
        Integer team1Score,
        
        @Min(value = 0, message = "Team 2 score cannot be negative")
        Integer team2Score,
        
        String winnerTeamId,
        
        Match.MatchStatus status,
        
        @Min(value = 1, message = "Round number must be at least 1")
        Integer roundNumber,
        
        @Min(value = 1, message = "Match number must be at least 1")
        Integer matchNumber,
        
        String venueName,
        
        String refereeName,
        
        String notes,
        
        LocalDateTime createdAt,
        
        LocalDateTime updatedAt
) {}
