package com.example.springcrudmongodb.services;

import com.example.springcrudmongodb.dto.ProductDto;
import com.example.springcrudmongodb.entities.Product;
import org.springframework.data.domain.Page;

import java.util.Map;

public interface IProductService {

    ProductDto add(ProductDto productDto);

    ProductDto update(String idProduct, Map<Object,Object> fields);

    boolean delete(String idProduct);


    Page<ProductDto> getProducts(int pageNbr, int pageSize);

    ProductDto getProduct(String id);

    ProductDto getProductByName(String name);
}