package com.sportstournament.tournamentservice.repositories;

import com.sportstournament.tournamentservice.entities.TournamentTeam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TournamentTeamRepository extends JpaRepository<TournamentTeam, Long> {
    
    List<TournamentTeam> findByTournamentId(Long tournamentId);
    
    List<TournamentTeam> findByTeamId(String teamId);
    
    Optional<TournamentTeam> findByTournamentIdAndTeamId(Long tournamentId, String teamId);
    
    List<TournamentTeam> findByStatus(TournamentTeam.RegistrationStatus status);
    
    @Query("SELECT tt FROM TournamentTeam tt WHERE tt.tournament.id = :tournamentId AND tt.status = :status")
    List<TournamentTeam> findByTournamentIdAndStatus(@Param("tournamentId") Long tournamentId, @Param("status") TournamentTeam.RegistrationStatus status);
    
    @Query("SELECT tt FROM TournamentTeam tt WHERE tt.tournament.id = :tournamentId ORDER BY tt.points DESC, (tt.goalsFor - tt.goalsAgainst) DESC")
    List<TournamentTeam> findByTournamentIdOrderByPointsDesc(@Param("tournamentId") Long tournamentId);
    
    @Query("SELECT tt FROM TournamentTeam tt WHERE tt.tournament.id = :tournamentId AND tt.groupName = :groupName ORDER BY tt.points DESC")
    List<TournamentTeam> findByTournamentIdAndGroupNameOrderByPointsDesc(@Param("tournamentId") Long tournamentId, @Param("groupName") String groupName);
    
    boolean existsByTournamentIdAndTeamId(Long tournamentId, String teamId);
    
    @Query("SELECT COUNT(tt) FROM TournamentTeam tt WHERE tt.tournament.id = :tournamentId AND tt.status = :status")
    long countByTournamentIdAndStatus(@Param("tournamentId") Long tournamentId, @Param("status") TournamentTeam.RegistrationStatus status);
    
    @Query("SELECT COUNT(tt) FROM TournamentTeam tt WHERE tt.teamId = :teamId")
    long countByTeamId(@Param("teamId") String teamId);
}
