package com.sportstournament.tournamentservice.dto;

import com.sportstournament.tournamentservice.entities.TournamentTeam;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

public record TournamentTeamDto(
        Long id,
        
        @NotNull(message = "Tournament ID is required")
        Long tournamentId,
        
        @NotBlank(message = "Team ID is required")
        String teamId,
        
        @NotBlank(message = "Team name is required")
        String teamName,
        
        LocalDateTime registrationDate,
        
        TournamentTeam.RegistrationStatus status,
        
        @Min(value = 1, message = "Seed number must be at least 1")
        Integer seedNumber,
        
        String groupName,
        
        @Min(value = 0, message = "Wins cannot be negative")
        Integer wins,
        
        @Min(value = 0, message = "Losses cannot be negative")
        Integer losses,
        
        @Min(value = 0, message = "Draws cannot be negative")
        Integer draws,
        
        @Min(value = 0, message = "Points cannot be negative")
        Integer points,
        
        @Min(value = 0, message = "Goals for cannot be negative")
        Integer goalsFor,
        
        @Min(value = 0, message = "Goals against cannot be negative")
        Integer goalsAgainst,
        
        LocalDateTime createdAt,
        
        LocalDateTime updatedAt
) {}
