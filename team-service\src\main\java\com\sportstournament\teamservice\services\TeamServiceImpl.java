package com.sportstournament.teamservice.services;

import com.sportstournament.teamservice.dto.TeamDto;
import com.sportstournament.teamservice.entities.Team;
import com.sportstournament.teamservice.events.TeamRegisteredEvent;
import com.sportstournament.teamservice.events.TeamStatusChangedEvent;
import com.sportstournament.teamservice.exceptions.TeamAlreadyExistsException;
import com.sportstournament.teamservice.exceptions.TeamNotFoundException;
import com.sportstournament.teamservice.mappers.TeamMapper;
import com.sportstournament.teamservice.repositories.TeamRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TeamServiceImpl implements TeamService {
    
    private final TeamRepository teamRepository;
    private final TeamMapper teamMapper;
    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    private static final String TEAM_REGISTERED_TOPIC = "team-registered";
    private static final String TEAM_STATUS_CHANGED_TOPIC = "team-status-changed";
    
    @Override
    public TeamDto registerTeam(TeamDto teamDto) {
        log.info("Registering new team: {}", teamDto.getName());

        // Check if team already exists
        if (teamRepository.existsByName(teamDto.getName())) {
            throw new TeamAlreadyExistsException("Team with name '" + teamDto.getName() + "' already exists");
        }

        if (teamRepository.existsByContactEmail(teamDto.getContactEmail())) {
            throw new TeamAlreadyExistsException("Team with email '" + teamDto.getContactEmail() + "' already exists");
        }
        
        Team team = teamMapper.mapToEntity(teamDto);
        team.setRegistrationDate(LocalDateTime.now());
        team.setCreatedAt(LocalDateTime.now());
        team.setUpdatedAt(LocalDateTime.now());
        team.setStatus(Team.TeamStatus.PENDING);
        
        // Generate player IDs if not provided
        if (team.getPlayers() != null) {
            team.getPlayers().forEach(player -> {
                if (player.getPlayerId() == null) {
                    player.setPlayerId(UUID.randomUUID().toString());
                }
            });
        }
        
        Team savedTeam = teamRepository.save(team);
        TeamDto result = teamMapper.mapToDto(savedTeam);
        
        // Publish event
        publishTeamRegisteredEvent(result);
        
        log.info("Team registered successfully with ID: {}", savedTeam.getId());
        return result;
    }
    
    @Override
    public TeamDto updateTeam(String id, TeamDto teamDto) {
        log.info("Updating team with ID: {}", id);
        
        Team existingTeam = teamRepository.findById(id)
                .orElseThrow(() -> new TeamNotFoundException("Team not found with ID: " + id));
        
        teamMapper.updateEntityFromDto(teamDto, existingTeam);
        existingTeam.setUpdatedAt(LocalDateTime.now());
        
        Team updatedTeam = teamRepository.save(existingTeam);
        
        log.info("Team updated successfully: {}", id);
        return teamMapper.mapToDto(updatedTeam);
    }
    
    @Override
    @Transactional(readOnly = true)
    public TeamDto getTeamById(String id) {
        Team team = teamRepository.findById(id)
                .orElseThrow(() -> new TeamNotFoundException("Team not found with ID: " + id));
        return teamMapper.mapToDto(team);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<TeamDto> getAllTeams() {
        List<Team> teams = teamRepository.findAll();
        return teamMapper.mapToDtoList(teams);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<TeamDto> getTeamsByStatus(Team.TeamStatus status) {
        List<Team> teams = teamRepository.findByStatus(status);
        return teamMapper.mapToDtoList(teams);
    }
    
    @Override
    public TeamDto approveTeam(String id) {
        return changeTeamStatus(id, Team.TeamStatus.APPROVED);
    }
    
    @Override
    public TeamDto rejectTeam(String id) {
        return changeTeamStatus(id, Team.TeamStatus.REJECTED);
    }
    
    private TeamDto changeTeamStatus(String id, Team.TeamStatus newStatus) {
        log.info("Changing team status to {} for team ID: {}", newStatus, id);
        
        Team team = teamRepository.findById(id)
                .orElseThrow(() -> new TeamNotFoundException("Team not found with ID: " + id));
        
        Team.TeamStatus oldStatus = team.getStatus();
        team.setStatus(newStatus);
        team.setUpdatedAt(LocalDateTime.now());
        
        Team updatedTeam = teamRepository.save(team);
        TeamDto result = teamMapper.mapToDto(updatedTeam);
        
        // Publish status change event
        publishTeamStatusChangedEvent(result, oldStatus, newStatus);
        
        log.info("Team status changed from {} to {} for team ID: {}", oldStatus, newStatus, id);
        return result;
    }
    
    @Override
    public void deleteTeam(String id) {
        log.info("Deleting team with ID: {}", id);
        
        if (!teamRepository.existsById(id)) {
            throw new TeamNotFoundException("Team not found with ID: " + id);
        }
        
        teamRepository.deleteById(id);
        log.info("Team deleted successfully: {}", id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String name) {
        return teamRepository.existsByName(name);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean existsByContactEmail(String contactEmail) {
        return teamRepository.existsByContactEmail(contactEmail);
    }
    
    @Override
    @Transactional(readOnly = true)
    public long countTeamsByStatus(Team.TeamStatus status) {
        return teamRepository.countByStatus(status);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<TeamDto> searchTeamsByName(String name) {
        List<Team> teams = teamRepository.findByNameContainingIgnoreCase(name);
        return teamMapper.mapToDtoList(teams);
    }
    
    private void publishTeamRegisteredEvent(TeamDto teamDto) {
        try {
            TeamRegisteredEvent event = new TeamRegisteredEvent(
                    teamDto.getId(),
                    teamDto.getName(),
                    teamDto.getCoach(),
                    teamDto.getContactEmail(),
                    teamDto.getPlayers() != null ? teamDto.getPlayers().size() : 0,
                    LocalDateTime.now()
            );

            kafkaTemplate.send(TEAM_REGISTERED_TOPIC, event);
            log.info("Published team registered event for team: {}", teamDto.getName());
        } catch (Exception e) {
            log.error("Failed to publish team registered event for team: {}", teamDto.getName(), e);
        }
    }
    
    private void publishTeamStatusChangedEvent(TeamDto teamDto, Team.TeamStatus oldStatus, Team.TeamStatus newStatus) {
        try {
            TeamStatusChangedEvent event = new TeamStatusChangedEvent(
                    teamDto.id(),
                    teamDto.name(),
                    oldStatus.toString(),
                    newStatus.toString(),
                    LocalDateTime.now()
            );
            
            kafkaTemplate.send(TEAM_STATUS_CHANGED_TOPIC, event);
            log.info("Published team status changed event for team: {} from {} to {}", 
                    teamDto.name(), oldStatus, newStatus);
        } catch (Exception e) {
            log.error("Failed to publish team status changed event for team: {}", teamDto.name(), e);
        }
    }
}
