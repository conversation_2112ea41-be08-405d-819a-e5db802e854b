package com.sportstournament.tournamentservice.mappers;

import com.sportstournament.tournamentservice.dto.MatchDto;
import com.sportstournament.tournamentservice.entities.Match;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MatchMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tournament", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Match mapToEntity(MatchDto matchDto);
    
    @Mapping(source = "tournament.id", target = "tournamentId")
    MatchDto mapToDto(Match match);
    
    List<MatchDto> mapToDtoList(List<Match> matches);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tournament", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromDto(MatchDto matchDto, @MappingTarget Match match);
}
