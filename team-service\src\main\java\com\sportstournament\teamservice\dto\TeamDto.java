package com.sportstournament.teamservice.dto;

import com.sportstournament.teamservice.entities.Team;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.List;

public record TeamDto(
        String id,
        
        @NotBlank(message = "Team name is required")
        @Size(min = 2, max = 100, message = "Team name must be between 2 and 100 characters")
        String name,
        
        @Size(max = 500, message = "Description cannot exceed 500 characters")
        String description,
        
        @NotBlank(message = "Coach name is required")
        @Size(min = 2, max = 100, message = "Coach name must be between 2 and 100 characters")
        String coach,
        
        @NotBlank(message = "Contact email is required")
        @Email(message = "Invalid email format")
        String contactEmail,
        
        String contactPhone,
        
        List<PlayerDto> players,
        
        LocalDateTime registrationDate,
        
        LocalDateTime createdAt,
        
        LocalDateTime updatedAt,
        
        Team.TeamStatus status
) {}
