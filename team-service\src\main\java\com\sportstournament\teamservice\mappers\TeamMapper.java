package com.sportstournament.teamservice.mappers;

import com.sportstournament.teamservice.dto.TeamDto;
import com.sportstournament.teamservice.entities.Team;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring", uses = {PlayerMapper.class})
public interface TeamMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Team mapToEntity(TeamDto teamDto);
    
    TeamDto mapToDto(Team team);
    
    List<TeamDto> mapToDtoList(List<Team> teams);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromDto(TeamDto teamDto, @MappingTarget Team team);
}
