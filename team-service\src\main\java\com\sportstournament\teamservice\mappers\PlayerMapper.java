package com.sportstournament.teamservice.mappers;

import com.sportstournament.teamservice.dto.PlayerDto;
import com.sportstournament.teamservice.entities.Player;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PlayerMapper {
    
    Player mapToEntity(PlayerDto playerDto);
    
    PlayerDto mapToDto(Player player);
    
    List<PlayerDto> mapToDtoList(List<Player> players);
    
    List<Player> mapToEntityList(List<PlayerDto> playerDtos);
}
