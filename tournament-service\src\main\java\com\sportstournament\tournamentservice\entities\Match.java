package com.sportstournament.tournamentservice.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "matches")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Match {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tournament_id", nullable = false)
    private Tournament tournament;
    
    @Column(name = "team1_id", nullable = false)
    private String team1Id;
    
    @Column(name = "team1_name")
    private String team1Name;
    
    @Column(name = "team2_id", nullable = false)
    private String team2Id;
    
    @Column(name = "team2_name")
    private String team2Name;
    
    @Column(name = "scheduled_time")
    private LocalDateTime scheduledTime;
    
    @Column(name = "actual_start_time")
    private LocalDateTime actualStartTime;
    
    @Column(name = "actual_end_time")
    private LocalDateTime actualEndTime;
    
    @Column(name = "team1_score")
    private Integer team1Score;
    
    @Column(name = "team2_score")
    private Integer team2Score;
    
    @Column(name = "winner_team_id")
    private String winnerTeamId;
    
    @Column(name = "match_status")
    @Enumerated(EnumType.STRING)
    private MatchStatus status;
    
    @Column(name = "round_number")
    private Integer roundNumber;
    
    @Column(name = "match_number")
    private Integer matchNumber;
    
    @Column(name = "venue_name")
    private String venueName;
    
    @Column(name = "referee_name")
    private String refereeName;
    
    @Column(length = 1000)
    private String notes;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum MatchStatus {
        SCHEDULED,
        IN_PROGRESS,
        COMPLETED,
        CANCELLED,
        POSTPONED
    }
}
