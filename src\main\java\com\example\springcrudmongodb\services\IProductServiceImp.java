package com.example.springcrudmongodb.services;

import com.example.springcrudmongodb.dto.ProductDto;
import com.example.springcrudmongodb.entities.Product;
import com.example.springcrudmongodb.mappers.ProductMapper;
import com.example.springcrudmongodb.repositories.ProductRepository;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class IProductServiceImp implements IProductService{
    private final ProductRepository productRepository;
    private final ProductMapper productMapper;



    @Override
    public ProductDto add(ProductDto productDto) {
        Product product = productMapper.mapToEntity(productDto);
        product.setCreatedAt(LocalDateTime.now());
        return productMapper.mapToDto(productRepository.save(product));
    }

    @Override
    public ProductDto update(String idProduct, Map<Object, Object> fields) {
        Product product = productRepository.findById(idProduct)
                .orElseThrow(() -> new IllegalArgumentException("Product not found: " + idProduct));

        fields.forEach((key, value) -> {
            Field field = ReflectionUtils.findField(Product.class, (String) key);
            field.setAccessible(true);

            if(field.getType().equals(LocalDate.class)){
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-mm-d");
                LocalDate localDate = LocalDate.parse((String) value, formatter);
                ReflectionUtils.setField(field, product , localDate);
            }else {
                ReflectionUtils.setField(field, product , value);
            }

        });
        product.setUpdatedAt(LocalDateTime.now());

        return productMapper.mapToDto(productRepository.save(product));
    }

    @Override
    public boolean delete(String idProduct) {
         productRepository.deleteById(idProduct);
        return productRepository.existsById(idProduct);
    }

    @Override
    public Page<ProductDto> getProducts(int pageNbr, int pageSize) {
        return productRepository.findAll(PageRequest.of(pageNbr,pageSize))
                .map(productMapper::mapToDto);
    }

    @Override
    public ProductDto getProduct(String id) {
        return productRepository.findById(id)
                .map(productMapper::mapToDto)
                .orElseThrow(() -> new IllegalArgumentException("product not found"));
    }

    @Override
    public ProductDto getProductByName(String name) {
        return productRepository.findByName(name)
                .map(productMapper::mapToDto)
                .orElseThrow(() ->new IllegalArgumentException("product not found with this name"));
    }


}