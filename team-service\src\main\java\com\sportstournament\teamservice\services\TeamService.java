package com.sportstournament.teamservice.services;

import com.sportstournament.teamservice.dto.TeamDto;
import com.sportstournament.teamservice.entities.Team;

import java.util.List;

public interface TeamService {
    
    TeamDto registerTeam(TeamDto teamDto);
    
    TeamDto updateTeam(String id, TeamDto teamDto);
    
    TeamDto getTeamById(String id);
    
    List<TeamDto> getAllTeams();
    
    List<TeamDto> getTeamsByStatus(Team.TeamStatus status);
    
    TeamDto approveTeam(String id);
    
    TeamDto rejectTeam(String id);
    
    void deleteTeam(String id);
    
    boolean existsByName(String name);
    
    boolean existsByContactEmail(String contactEmail);
    
    long countTeamsByStatus(Team.TeamStatus status);
    
    List<TeamDto> searchTeamsByName(String name);
}
