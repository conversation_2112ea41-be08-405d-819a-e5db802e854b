package com.sportstournament.teamservice.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TeamStatusChangedEvent {
    
    private String teamId;
    private String teamName;
    private String oldStatus;
    private String newStatus;
    private LocalDateTime timestamp;
}
